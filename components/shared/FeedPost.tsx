import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Image as RNImage,
  Platform,
  Share,
} from "react-native";
import { type EnrichedActivity } from "getstream";
import { Heart, MessageCircle, Pin, Share2 } from "lucide-react-native";
import { Image } from "expo-image";
import { router } from "expo-router";

// Component for dynamic image sizing
const DynamicImage = ({ uri, style }: { uri: string; style: any }) => {
  const [imageHeight, setImageHeight] = useState<number>(200);
  const screenWidth = Dimensions.get("window").width;
  const imageWidth = screenWidth - 64; // Account for padding

  const handleImageLoad = () => {
    RNImage.getSize(
      uri,
      (width: number, height: number) => {
        const aspectRatio = width / height;
        const calculatedHeight = imageWidth / aspectRatio;
        // Set reasonable bounds for height
        const finalHeight = Math.min(Math.max(calculatedHeight, 150), 600);
        setImageHeight(finalHeight);
      },
      (error: any) => {
        console.log("Error getting image size:", error);
        setImageHeight(200); // Fallback height
      }
    );
  };

  return (
    <Image
      source={{ uri }}
      style={[style, { height: imageHeight }]}
      onLoad={handleImageLoad}
      contentFit="cover"
    />
  );
};

interface EnrichedActivityWithText extends EnrichedActivity {
  text?: string;
  message?: string;
  image?: string;
  isPinned?: boolean;
  attachments?: Array<{
    type: string;
    image_url?: string;
    asset_url?: string;
    custom?: Record<string, any>;
  }>;
  own_reactions?: {
    like?: any[];
  };
  reaction_counts?: {
    like?: number;
    comment?: number;
  };
}

interface FeedPostProps {
  activity: EnrichedActivityWithText;
  onLike?: (activityId: string) => void;
  onComment?: (activity: EnrichedActivityWithText) => void;
  onPress?: (activity: EnrichedActivityWithText) => void;
  showActions?: boolean;
  showShare?: boolean;
  moduleConfig?: any;
  variant?: "feed" | "detail";
}

export const FeedPost = ({
  activity,
  onLike,
  onComment,
  onPress,
  showActions = true,
  showShare = true,
  moduleConfig,
  variant = "feed",
}: FeedPostProps) => {
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return "just now";
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800)
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return date.toLocaleDateString();
  };

  const getActorName = (actor: any): string => {
    if (typeof actor === "string") {
      const parts = actor.split(":");
      return parts[parts.length - 1] || actor;
    }
    if (actor?.data?.name) return actor.data.name;
    if (actor?.data?.firstName && actor?.data?.lastName) {
      return `${actor.data.firstName} ${actor.data.lastName}`;
    }
    if (actor?.id) return `User ${actor.id.substring(0, 8)}`;
    return "Unknown User";
  };

  const getActorImage = (actor: any): string | null => {
    if (actor?.data?.image) return actor.data.image;
    if (actor?.data?.avatarUrl) return actor.data.avatarUrl;
    return null;
  };

  const getActivityContent = (activity: EnrichedActivityWithText): string => {
    if (activity.message) return activity.message;
    if (activity.text) return activity.text;
    if (typeof activity.object === "string") return activity.object;
    const obj = activity.object as any;
    if (obj?.text) return obj.text;
    if (obj?.content) return obj.content;
    return "";
  };

  const isCreator = (actor: any): boolean => {
    if (actor?.data?.role) {
      return actor.data.role === "creator" || actor.data.role === "moderator";
    }
    return false;
  };

  const handleShare = async () => {
    const url = `${process.env.EXPO_PUBLIC_APP_BASE_URL}/post/${activity.id}`;
    try {
      if (Platform.OS === "android") {
        await Share.share({
          message: url,
        });
      } else {
        await Share.share({
          url,
        });
      }
    } catch (error) {
      console.error("Error sharing:", error);
    }
  };

  const handlePostPress = () => {
    if (onPress) {
      onPress(activity);
    } else if (moduleConfig) {
      router.push({
        pathname: "/post/[id]",
        params: {
          id: activity.id,
          moduleConfig: JSON.stringify(moduleConfig),
        },
      });
    }
  };

  const avatarSize = variant === "detail" ? 48 : 38;
  const avatarRadius = avatarSize / 2;
  const userNameSize = variant === "detail" ? 16 : 14;
  const postTextSize = variant === "detail" ? 16 : 14;
  const postTextLineHeight = variant === "detail" ? 24 : 20;

  const PostContent = (
    <View
      style={[styles.postCard, variant === "detail" && styles.postCardDetail]}
    >
      <View style={styles.postHeader}>
        <View style={styles.userInfo}>
          {getActorImage(activity.actor) ? (
            <Image
              source={{ uri: getActorImage(activity.actor)! }}
              style={[
                styles.avatar,
                {
                  width: avatarSize,
                  height: avatarSize,
                  borderRadius: avatarRadius,
                },
              ]}
            />
          ) : (
            <View
              style={[
                styles.avatarPlaceholder,
                {
                  width: avatarSize,
                  height: avatarSize,
                  borderRadius: avatarRadius,
                },
              ]}
            >
              <Text
                style={[
                  styles.avatarText,
                  { fontSize: variant === "detail" ? 20 : 16 },
                ]}
              >
                {getActorName(activity.actor)[0]?.toUpperCase() || "U"}
              </Text>
            </View>
          )}
          <View style={styles.userMeta}>
            <View style={styles.nameContainer}>
              <Text style={[styles.userName, { fontSize: userNameSize }]}>
                {getActorName(activity.actor)}
              </Text>
              {isCreator(activity.actor) && (
                <View style={styles.creatorBadge}>
                  <Text style={styles.creatorText}>Creator</Text>
                </View>
              )}
            </View>
            <Text
              style={[
                styles.timestamp,
                { fontSize: variant === "detail" ? 14 : 12 },
              ]}
            >
              {formatTime(activity.time)}
            </Text>
          </View>
        </View>
        <View style={styles.postHeaderActions}>
          {activity.isPinned && (
            <Pin size={20} color="#FACC15" fill="#FACC15" />
          )}
          {showShare && (
            <TouchableOpacity
              onPress={(e) => {
                e.stopPropagation();
                handleShare();
              }}
              style={styles.shareButton}
            >
              <Share2 size={16} color="#9A9A9A" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      <View style={styles.postContent}>
        <Text
          style={[
            styles.postText,
            { fontSize: postTextSize, lineHeight: postTextLineHeight },
          ]}
        >
          {getActivityContent(activity)}
        </Text>

        {/* Display legacy image field */}
        {activity.image && (
          <DynamicImage uri={activity.image} style={styles.postImage} />
        )}

        {/* Display attachments */}
        {activity.attachments && activity.attachments.length > 0 && (
          <View style={styles.attachmentsContainer}>
            {activity.attachments.map((attachment, index) => (
              <View key={index} style={styles.attachmentItem}>
                {attachment.type === "image" && attachment.image_url && (
                  <DynamicImage
                    uri={attachment.image_url}
                    style={styles.postImage}
                  />
                )}
                {attachment.type === "file" && attachment.asset_url && (
                  <TouchableOpacity
                    style={styles.fileAttachment}
                    onPress={() => {
                      console.log("File attachment:", attachment.asset_url);
                    }}
                  >
                    <Text style={styles.fileAttachmentText}>📎 View File</Text>
                  </TouchableOpacity>
                )}
              </View>
            ))}
          </View>
        )}
      </View>

      {showActions && (
        <View style={styles.postActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={(e) => {
              e.stopPropagation();
              onLike?.(activity.id);
            }}
          >
            <Heart
              size={20}
              color={activity.own_reactions?.like?.length ? "#EF5252" : "#fff"}
              fill={
                activity.own_reactions?.like?.length ? "#EF5252" : "transparent"
              }
            />
            <Text
              style={[
                styles.actionText,
                activity.own_reactions?.like?.length
                  ? styles.likedText
                  : undefined,
              ]}
            >
              {activity.reaction_counts?.like || 0}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={(e) => {
              e.stopPropagation();
              onComment?.(activity);
            }}
          >
            <MessageCircle size={20} color="#fff" />
            <Text style={styles.actionText}>
              {activity.reaction_counts?.comment || 0}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  if (variant === "detail" || !onPress) {
    return PostContent;
  }

  return (
    <TouchableOpacity onPress={handlePostPress} activeOpacity={0.8}>
      {PostContent}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  postCard: {
    backgroundColor: "#171D23",
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  postCardDetail: {
    marginBottom: 0,
    borderRadius: 0,
  },
  postHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  avatar: {
    // Dynamic size set in component
  },
  avatarPlaceholder: {
    backgroundColor: "#EF5252",
    justifyContent: "center",
    alignItems: "center",
  },
  avatarText: {
    color: "#fff",
    fontWeight: "600",
  },
  userMeta: {
    gap: 2,
  },
  nameContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  userName: {
    fontWeight: "700",
    color: "#fff",
  },
  creatorBadge: {
    backgroundColor: "#EF5252",
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 4,
  },
  creatorText: {
    fontSize: 10,
    fontWeight: "500",
    color: "#fff",
  },
  timestamp: {
    color: "#9A9A9A",
  },
  postHeaderActions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  shareButton: {
    padding: 4,
  },
  postContent: {
    marginBottom: 16,
  },
  postText: {
    color: "#D9D9D9",
  },
  postImage: {
    borderRadius: 8,
    marginTop: 12,
  },
  attachmentsContainer: {
    marginTop: 12,
  },
  attachmentItem: {
    marginBottom: 8,
  },
  fileAttachment: {
    backgroundColor: "#333",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  fileAttachmentText: {
    color: "#9A9A9A",
    fontSize: 14,
  },
  postActions: {
    flexDirection: "row",
    gap: 24,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: "#242424",
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  actionText: {
    fontSize: 16,
    fontWeight: "700",
    color: "rgba(255, 255, 255, 0.8)",
  },
  likedText: {
    color: "#EF5252",
  },
});
